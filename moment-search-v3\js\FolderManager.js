/**
 * 文件夹管理器 - 处理图标分组和文件夹操作
 * 扩展现有的快捷方式系统，支持iOS/Android风格的文件夹功能
 */

class FolderManager {
    constructor(shortcutManager) {
        this.shortcutManager = shortcutManager;
        this.folderModal = null;
        this.currentFolder = null;

        // 文件夹配置
        this.config = {
            maxItemsInFolder: 20
        };

        // 事件监听器引用，用于清理
        this.boundHandlers = {
            dragOver: null,
            drop: null,
            dragLeave: null
        };

        this.init();
    }

    init() {
        this.createFolderModal();
        this.bindEvents();
    }

    /**
     * 检查是否可以创建文件夹
     * @param {string} sourceId 源图标ID
     * @param {string} targetId 目标图标ID
     * @returns {boolean}
     */
    canCreateFolder(sourceId, targetId) {
        const sourceItem = this.shortcutManager.getShortcutById(sourceId);
        const targetItem = this.shortcutManager.getShortcutById(targetId);
        
        if (!sourceItem || !targetItem || sourceId === targetId) {
            return false;
        }
        
        // 不能将文件夹拖到另一个文件夹上（避免嵌套）
        if (sourceItem.type === 'folder' && targetItem.type === 'folder') {
            return false;
        }
        
        return true;
    }

    /**
     * 创建文件夹
     * @param {string} sourceId 源图标ID
     * @param {string} targetId 目标图标ID
     * @returns {Object|null} 创建的文件夹对象
     */
    createFolder(sourceId, targetId) {
        if (!this.canCreateFolder(sourceId, targetId)) {
            return null;
        }

        const sourceItem = this.shortcutManager.getShortcutById(sourceId);
        const targetItem = this.shortcutManager.getShortcutById(targetId);

        // 先清理图标数据，确保预览和存储使用相同的数据
        const cleanedSourceItem = this.cleanItemForFolder(sourceItem);
        const cleanedTargetItem = this.cleanItemForFolder(targetItem);

        // 创建文件夹数据结构
        const folder = {
            id: this.generateFolderId(),
            name: this.generateFolderName([cleanedSourceItem, cleanedTargetItem]),
            type: 'folder',
            icon: this.generateFolderIcon([cleanedSourceItem, cleanedTargetItem]), // 使用清理后的数据生成预览
            order: targetItem.order, // 使用目标位置
            category: 'folder',
            children: [
                cleanedSourceItem,
                cleanedTargetItem
            ],
            createdAt: Date.now(),
            updatedAt: Date.now()
        };

        // 从原始列表中移除源图标和目标图标
        this.shortcutManager.removeShortcut(sourceId);
        this.shortcutManager.removeShortcut(targetId);

        // 添加文件夹到指定位置（addFolderAtPosition已包含保存和渲染）
        this.shortcutManager.addFolderAtPosition(folder, targetItem.order);

        return folder;
    }

    /**
     * 获取图标的最佳URL（优先使用src，然后是faviconUrl）
     * @param {Object} item 图标数据
     * @returns {string} 图标URL
     */
    getIconUrl(item) {
        return item.src || item.faviconUrl || '';
    }

    /**
     * 清理图标数据，确保只包含必要的属性
     * @param {Object} item 原始图标数据
     * @returns {Object} 清理后的图标数据
     */
    cleanItemForFolder(item) {
        return {
            id: item.id,
            name: item.name,
            url: item.url,
            icon: item.icon,
            faviconUrl: item.faviconUrl,
            src: item.src, // 保留src字段，用于文件夹预览
            type: 'shortcut', // 确保类型正确
            category: item.category || 'shortcut',
            order: item.order,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt
        };
    }

    /**
     * 生成文件夹ID
     * @returns {string}
     */
    generateFolderId() {
        return 'folder_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 生成文件夹名称
     * @param {Array} items 文件夹内的图标数组
     * @returns {string}
     */
    generateFolderName(items) {
        if (items.length === 0) return '新建文件夹';
        
        // 尝试根据分类生成名称
        const categories = [...new Set(items.map(item => item.category))];
        const categoryNames = {
            'search': '搜索工具',
            'social': '社交媒体', 
            'entertainment': '娱乐',
            'productivity': '效率工具',
            'development': '开发工具',
            'shopping': '购物',
            'news': '新闻资讯'
        };
        
        if (categories.length === 1 && categoryNames[categories[0]]) {
            return categoryNames[categories[0]];
        }
        
        // 默认使用第一个图标的名称
        return items[0].name + '等';
    }

    /**
     * 生成文件夹图标 - SVG预览
     * @param {Array} items 文件夹内的图标数组
     * @returns {string} 文件夹图标
     */
    generateFolderIcon(items) {
        if (items.length === 0) {
            return '📁'; // 空文件夹
        }

        // 创建SVG预览，显示真实的平台图标
        return this.createCompactSVGPreview(items);
    }

    /**
     * 创建紧凑的SVG预览图标
     * @param {Array} items 预览的图标数组
     * @returns {string} SVG data URL
     */
    createCompactSVGPreview(items) {
        const size = 72; // 与普通图标容器尺寸一致
        const iconSize = 24; // 相应增大图标尺寸
        const gap = 6;

        // 计算布局：最多显示4个图标，2x2网格
        const maxIcons = Math.min(items.length, 4);
        const positions = this.calculateIconPositions(maxIcons, size, iconSize, gap);

        // 创建SVG内容
        let svgContent = this.createSVGHeader(size);

        // 添加图标
        for (let i = 0; i < maxIcons; i++) {
            const item = items[i];
            const pos = positions[i];
            svgContent += this.createIconElement(item, pos.x, pos.y, iconSize);
        }

        svgContent += '</svg>';

        // 使用base64编码，避免特殊字符问题
        return this.encodeSVGToDataURL(svgContent);
    }

    /**
     * 计算图标位置
     * @param {number} iconCount 图标数量
     * @param {number} containerSize 容器大小
     * @param {number} iconSize 图标大小
     * @param {number} gap 间距
     * @returns {Array} 位置数组
     */
    calculateIconPositions(iconCount, containerSize, iconSize, gap) {
        const positions = [];

        if (iconCount === 1) {
            // 单个图标居中
            const center = (containerSize - iconSize) / 2;
            positions.push({ x: center, y: center });
        } else if (iconCount === 2) {
            // 两个图标水平排列
            const startX = (containerSize - (iconSize * 2 + gap)) / 2;
            const centerY = (containerSize - iconSize) / 2;
            positions.push({ x: startX, y: centerY });
            positions.push({ x: startX + iconSize + gap, y: centerY });
        } else {
            // 3-4个图标，2x2网格
            const startX = (containerSize - (iconSize * 2 + gap)) / 2;
            const startY = (containerSize - (iconSize * 2 + gap)) / 2;

            positions.push({ x: startX, y: startY }); // 左上
            positions.push({ x: startX + iconSize + gap, y: startY }); // 右上

            if (iconCount >= 3) {
                positions.push({ x: startX, y: startY + iconSize + gap }); // 左下
            }
            if (iconCount >= 4) {
                positions.push({ x: startX + iconSize + gap, y: startY + iconSize + gap }); // 右下
            }
        }

        return positions;
    }

    /**
     * 创建SVG头部
     * @param {number} size SVG大小
     * @returns {string} SVG头部
     */
    createSVGHeader(size) {
        return `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${size} ${size}">
            <circle cx="${size/2}" cy="${size/2}" r="${size/2 - 2}" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.15)" stroke-width="1"/>`;
    }

    /**
     * 创建图标元素
     * @param {Object} item 图标数据
     * @param {number} x X坐标
     * @param {number} y Y坐标
     * @param {number} size 图标大小
     * @returns {string} SVG图标元素
     */
    createIconElement(item, x, y, size) {
        // 使用通用的图标URL获取方法
        const iconUrl = this.getIconUrl(item);

        if (iconUrl && iconUrl.startsWith('http')) {
            // 使用真实的图标，添加白色背景圆形
            return `<circle cx="${x + size/2}" cy="${y + size/2}" r="${size/2 - 1}" fill="white" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                    <image x="${x + 3}" y="${y + 3}" width="${size - 6}" height="${size - 6}" href="${iconUrl}" preserveAspectRatio="xMidYMid meet"/>`;
        } else {
            // 使用emoji后备
            const emoji = item.icon || '🌐';
            const fontSize = size * 0.6;
            return `<circle cx="${x + size/2}" cy="${y + size/2}" r="${size/2 - 1}" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                    <text x="${x + size/2}" y="${y + size/2 + fontSize/4}" font-size="${fontSize}" text-anchor="middle" fill="white">${emoji}</text>`;
        }
    }

    /**
     * 将SVG编码为data URL
     * @param {string} svgContent SVG内容
     * @returns {string} data URL
     */
    encodeSVGToDataURL(svgContent) {
        try {
            // 清理SVG内容，移除可能导致问题的字符
            const cleanSvg = svgContent
                .replace(/\s+/g, ' ')  // 压缩空白字符
                .replace(/>\s+</g, '><')  // 移除标签间空白
                .trim();

            // 使用btoa进行base64编码，更可靠
            const base64 = btoa(unescape(encodeURIComponent(cleanSvg)));
            return `data:image/svg+xml;base64,${base64}`;
        } catch (error) {
            console.warn('SVG编码失败，使用后备图标:', error);
            return '📂'; // 后备emoji图标
        }
    }













    /**
     * 添加图标到文件夹
     * @param {string} folderId 文件夹ID
     * @param {string} itemId 图标ID
     * @returns {boolean} 是否成功
     */
    addToFolder(folderId, itemId) {
        const folder = this.shortcutManager.getShortcutById(folderId);
        const item = this.shortcutManager.getShortcutById(itemId);
        
        if (!folder || !item || folder.type !== 'folder') {
            return false;
        }
        
        if (folder.children.length >= this.config.maxItemsInFolder) {
            alert(`文件夹最多只能包含 ${this.config.maxItemsInFolder} 个图标`);
            return false;
        }
        
        // 从原始列表中移除图标
        this.shortcutManager.removeShortcut(itemId);
        
        // 添加到文件夹
        folder.children.push(this.cleanItemForFolder(item));
        folder.updatedAt = Date.now();
        
        // 更新文件夹图标
        folder.icon = this.generateFolderIcon(folder.children);
        
        // 保存并重新渲染
        this.shortcutManager.saveShortcuts();
        this.shortcutManager.render();
        
        return true;
    }

    /**
     * 从文件夹中移除图标
     * @param {string} folderId 文件夹ID
     * @param {string} itemId 图标ID
     * @returns {boolean} 是否成功
     */
    removeFromFolder(folderId, itemId) {
        const folder = this.shortcutManager.getShortcutById(folderId);
        
        if (!folder || folder.type !== 'folder') {
            return false;
        }
        
        const itemIndex = folder.children.findIndex(child => child.id == itemId);
        if (itemIndex === -1) {
            return false;
        }
        
        // 移除图标
        const [removedItem] = folder.children.splice(itemIndex, 1);
        
        // 将图标添加回主列表
        delete removedItem.parentId;
        removedItem.order = this.shortcutManager.shortcuts.length + 1;
        this.shortcutManager.shortcuts.push(removedItem);
        
        // 如果文件夹只剩一个图标，解散文件夹
        if (folder.children.length === 1) {
            this.dissolveFolder(folderId);
        } else {
            // 更新文件夹图标
            folder.icon = this.generateFolderIcon(folder.children);
            folder.updatedAt = Date.now();
        }
        
        // 保存并重新渲染
        this.shortcutManager.saveShortcuts();
        this.shortcutManager.render();
        
        return true;
    }

    /**
     * 解散文件夹（当只剩一个图标时）
     * @param {string} folderId 文件夹ID
     */
    dissolveFolder(folderId) {
        const folder = this.shortcutManager.getShortcutById(folderId);

        if (!folder || folder.type !== 'folder') {
            return;
        }

        // 将剩余的图标添加回主列表
        folder.children.forEach(child => {
            delete child.parentId;
            child.order = folder.order; // 使用文件夹的位置
            this.shortcutManager.shortcuts.push(child);
        });

        // 移除文件夹
        this.shortcutManager.removeShortcut(folderId);
    }

    /**
     * 创建文件夹模态窗口
     */
    createFolderModal() {
        // 创建模态窗口HTML - 简化结构，移除顶部区域
        const modalHTML = `
            <div id="folderModal" class="folder-modal" style="display: none;">
                <div class="folder-modal-backdrop"></div>
                <div class="folder-title-container">
                    <div id="folderNameDisplay" class="folder-name-display">未命名文件夹</div>
                    <input type="text" id="folderNameInput" class="folder-name-input" style="display: none;">
                </div>
                <div class="folder-modal-content">
                    <div id="folderIconGrid" class="folder-icon-grid"></div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // 获取元素引用
        this.folderModal = document.getElementById('folderModal');
        this.folderNameDisplay = document.getElementById('folderNameDisplay');
        this.folderNameInput = document.getElementById('folderNameInput');
        this.folderIconGrid = document.getElementById('folderIconGrid');

        // 添加样式
        this.addFolderModalStyles();
    }

    /**
     * 添加文件夹模态窗口样式
     */
    addFolderModalStyles() {
        const styles = `
            <style id="folderModalStyles">
                .folder-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 10000;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                }

                .folder-modal-backdrop {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    backdrop-filter: blur(10px);
                }

                .folder-title-container {
                    position: relative;
                    z-index: 10001;
                    margin-bottom: 12px;
                }

                .folder-name-display {
                    color: white;
                    font-size: 16px;
                    font-weight: 500;
                    text-align: center;
                    cursor: pointer;
                    padding: 8px 16px;
                    border-radius: 8px;
                    transition: background 0.2s ease;
                    min-width: 120px;
                }

                .folder-name-display:hover {
                    background: rgba(255, 255, 255, 0.1);
                }

                .folder-name-input {
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 8px;
                    padding: 8px 16px;
                    color: white;
                    font-size: 16px;
                    font-weight: 500;
                    text-align: center;
                    outline: none;
                    min-width: 120px;
                }

                .folder-name-input:focus {
                    border-color: rgba(255, 255, 255, 0.5);
                    background: rgba(255, 255, 255, 0.15);
                }

                .folder-name-input::placeholder {
                    color: rgba(255, 255, 255, 0.6);
                }

                .folder-modal-content {
                    position: relative;
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 16px;
                    padding: 0;
                    max-width: 480px;
                    max-height: 600px;
                    width: 90vw;
                    height: auto;
                    backdrop-filter: blur(20px);
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                    animation: folderModalSlideIn 0.3s ease-out;
                }

                @keyframes folderModalSlideIn {
                    from {
                        opacity: 0;
                        transform: scale(0.8) translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: scale(1) translateY(0);
                    }
                }

                .folder-modal-content {
                    padding: 20px;
                    max-height: 400px;
                    overflow-y: auto;
                }

                .folder-icon-grid {
                    display: grid;
                    grid-template-columns: repeat(6, 1fr);
                    gap: 16px;
                    justify-items: center;
                    padding: 8px;
                }

                /* 文件夹内图标项样式 - 与主界面图标保持一致 */
                .folder-shortcut-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    padding: 8px;
                    border-radius: 12px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    position: relative;
                }

                .folder-shortcut-item.dragging {
                    opacity: 0.5;
                    transform: scale(0.9);
                }

                /* 拖拽出文件夹的视觉指示 */
                .folder-modal.drag-out-indicator {
                    border: 3px solid rgba(255, 59, 48, 0.8);
                    box-shadow: 0 0 20px rgba(255, 59, 48, 0.4);
                }

                .folder-modal.drag-out-indicator::after {
                    content: '拖拽到此区域外移除图标';
                    position: absolute;
                    top: -40px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(255, 59, 48, 0.9);
                    color: white;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-size: 14px;
                    white-space: nowrap;
                    z-index: 10001;
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 点击背景关闭
        if (this.folderModal) {
            this.folderModal.addEventListener('click', (e) => {
                if (e.target.classList.contains('folder-modal-backdrop')) {
                    this.closeFolderModal();
                }
            });
        }

        // 文件夹名称编辑功能
        if (this.folderNameDisplay) {
            this.folderNameDisplay.addEventListener('click', () => {
                this.enterEditMode();
            });
        }

        if (this.folderNameInput) {
            this.folderNameInput.addEventListener('blur', () => {
                this.exitEditMode();
            });

            this.folderNameInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.folderNameInput.blur();
                } else if (e.key === 'Escape') {
                    this.cancelEdit();
                }
            });
        }

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.folderModal && this.folderModal.style.display !== 'none') {
                this.closeFolderModal();
            }
        });
    }

    /**
     * 打开文件夹模态窗口
     * @param {string} folderId 文件夹ID
     */
    openFolderModal(folderId) {
        const folder = this.shortcutManager.getShortcutById(folderId);
        if (!folder || folder.type !== 'folder') {
            return;
        }

        this.currentFolder = folder;

        // 设置文件夹名称显示
        this.folderNameDisplay.textContent = folder.name;
        this.folderNameInput.value = folder.name;

        // 渲染文件夹内容
        this.renderFolderContent(folder);

        // 显示模态窗口
        this.folderModal.style.display = 'flex';
    }

    /**
     * 进入编辑模式
     */
    enterEditMode() {
        if (!this.currentFolder) return;

        this.folderNameDisplay.style.display = 'none';
        this.folderNameInput.style.display = 'block';
        this.folderNameInput.value = this.currentFolder.name;
        this.folderNameInput.focus();
        this.folderNameInput.select();
    }

    /**
     * 退出编辑模式
     */
    exitEditMode() {
        if (!this.currentFolder) return;

        const newName = this.folderNameInput.value.trim() || '未命名文件夹';
        this.updateFolderName(this.currentFolder.id, newName);

        this.folderNameDisplay.textContent = newName;
        this.folderNameDisplay.style.display = 'block';
        this.folderNameInput.style.display = 'none';
    }

    /**
     * 取消编辑
     */
    cancelEdit() {
        if (!this.currentFolder) return;

        this.folderNameInput.value = this.currentFolder.name;
        this.folderNameDisplay.style.display = 'block';
        this.folderNameInput.style.display = 'none';
    }

    /**
     * 关闭文件夹模态窗口
     */
    closeFolderModal() {
        if (this.folderModal) {
            this.folderModal.style.display = 'none';
        }
        this.currentFolder = null;
    }

    /**
     * 渲染文件夹内容
     * @param {Object} folder 文件夹对象
     */
    renderFolderContent(folder) {
        if (!this.folderIconGrid) return;

        let html = '';

        folder.children.forEach(item => {
            html += this.createFolderItemHTML(item);
        });

        this.folderIconGrid.innerHTML = html;

        // 绑定文件夹内图标的事件
        this.bindFolderItemEvents();
    }

    /**
     * 创建文件夹内图标的HTML - 使用与主界面完全一致的样式
     * @param {Object} item 图标对象
     * @returns {string} HTML字符串
     */
    createFolderItemHTML(item) {
        // 使用通用的图标URL获取方法
        const iconSrc = this.getIconUrl(item);
        const showIcon = iconSrc && iconSrc.trim() !== '';

        return `
            <div class="shortcut-item folder-shortcut-item"
                 data-id="${item.id}"
                 data-url="${item.url}"
                 data-name="${item.name}"
                 data-type="shortcut"
                 draggable="true">
                <div class="shortcut-icon" data-shortcut-id="${item.id}">
                    <img class="shortcut-favicon"
                         src="${showIcon ? iconSrc : ''}"
                         alt="${item.name}"
                         style="display: ${showIcon ? 'inline' : 'none'};"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                    <span class="shortcut-emoji" style="display: ${showIcon ? 'none' : 'inline'}">${item.icon}</span>
                </div>
                <div class="shortcut-name">${item.name}</div>
            </div>
        `;
    }

    /**
     * 绑定文件夹内图标的事件
     */
    bindFolderItemEvents() {
        // 图标点击事件（打开链接）
        this.folderIconGrid.addEventListener('click', (e) => {
            const iconItem = e.target.closest('.folder-shortcut-item');
            if (iconItem) {
                const itemId = iconItem.dataset.id;
                const item = this.currentFolder.children.find(child => child.id == itemId);
                if (item) {
                    window.open(item.url, '_blank');
                }
            }
        });

        // 拖拽事件
        this.bindFolderDragEvents();
    }

    /**
     * 绑定文件夹内的拖拽事件
     */
    bindFolderDragEvents() {
        // 拖拽开始
        this.folderIconGrid.addEventListener('dragstart', (e) => {
            const iconItem = e.target.closest('.folder-shortcut-item');
            if (iconItem) {
                iconItem.classList.add('dragging');
                e.dataTransfer.setData('text/plain', iconItem.dataset.id);
                e.dataTransfer.setData('application/x-folder-item', 'true');
                e.dataTransfer.effectAllowed = 'move';

                // 标记当前正在拖拽的图标
                this.currentDraggedItem = iconItem;

                console.log('开始从文件夹内拖拽图标:', iconItem.dataset.id);
            }
        });

        // 拖拽结束
        this.folderIconGrid.addEventListener('dragend', (e) => {
            const iconItem = e.target.closest('.folder-shortcut-item');
            if (iconItem) {
                iconItem.classList.remove('dragging');
            }

            // 清理拖拽状态
            this.currentDraggedItem = null;

            // 清理视觉指示
            this.clearDragIndicators();
        });

        // 监听拖拽到文件夹外部的事件
        this.setupDragOutOfFolder();
    }

    /**
     * 设置拖拽出文件夹的功能
     */
    setupDragOutOfFolder() {
        // 清理之前的事件监听器
        this.cleanupDragEvents();

        // 监听整个文档的拖拽事件
        this.boundHandlers.dragOver = (e) => {
            const folderModal = document.getElementById('folderModal');
            if (!folderModal || folderModal.style.display === 'none') {
                return;
            }

            // 检查是否是文件夹内的图标被拖拽
            const draggedData = e.dataTransfer.types.includes('application/x-folder-item');
            if (!draggedData) return;

            e.preventDefault();

            // 检查鼠标是否在文件夹弹窗外部
            const rect = folderModal.getBoundingClientRect();
            const isOutside = e.clientX < rect.left || e.clientX > rect.right ||
                             e.clientY < rect.top || e.clientY > rect.bottom;

            if (isOutside) {
                e.dataTransfer.dropEffect = 'move';

                // 关键改进：当拖拽到文件夹外部时，立即关闭文件夹弹窗
                if (!this.isDraggedOutside) {
                    this.isDraggedOutside = true;
                    console.log('拖拽移出文件夹边界，关闭文件夹弹窗');

                    // 延迟关闭文件夹，确保拖拽事件能正常完成
                    setTimeout(() => {
                        folderModal.style.display = 'none';
                    }, 50);
                }

                // 检查是否在主页面图标区域上方
                const shortcutsGrid = document.querySelector('.shortcuts-grid');
                if (shortcutsGrid) {
                    const gridRect = shortcutsGrid.getBoundingClientRect();
                    const isOverGrid = e.clientX >= gridRect.left && e.clientX <= gridRect.right &&
                                      e.clientY >= gridRect.top && e.clientY <= gridRect.bottom;

                    if (isOverGrid) {
                        shortcutsGrid.classList.add('drag-over-from-folder');
                        e.dataTransfer.dropEffect = 'move';
                    } else {
                        shortcutsGrid.classList.remove('drag-over-from-folder');
                    }
                }
            } else {
                this.isDraggedOutside = false;
                const shortcutsGrid = document.querySelector('.shortcuts-grid');
                if (shortcutsGrid) {
                    shortcutsGrid.classList.remove('drag-over-from-folder');
                }
            }
        };

        document.addEventListener('dragover', this.boundHandlers.dragOver);

        this.boundHandlers.drop = (e) => {
            // 检查是否是文件夹内的图标被拖拽
            const isFolderItem = e.dataTransfer.getData('application/x-folder-item');
            if (!isFolderItem) return;

            e.preventDefault();
            e.stopPropagation();

            const itemId = e.dataTransfer.getData('text/plain');
            console.log('拖拽图标到主页面:', itemId, 'at', e.clientX, e.clientY);

            // 移除图标从文件夹并添加到主页面
            this.moveItemOutOfFolder(itemId, e.clientX, e.clientY);

            // 清理所有拖拽状态和样式
            this.clearDragIndicators();

            // 重置拖拽状态
            this.isDraggedOutside = false;
        };

        // 监听整个文档的drop事件
        document.addEventListener('drop', this.boundHandlers.drop);

        // 监听主页面图标区域的drop事件
        const shortcutsGrid = document.querySelector('.shortcuts-grid');
        if (shortcutsGrid) {
            shortcutsGrid.addEventListener('dragover', (e) => {
                const isFolderItem = e.dataTransfer.types.includes('application/x-folder-item');
                if (isFolderItem) {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                    shortcutsGrid.classList.add('drag-over-from-folder');
                }
            });

            shortcutsGrid.addEventListener('dragleave', (e) => {
                if (!shortcutsGrid.contains(e.relatedTarget)) {
                    shortcutsGrid.classList.remove('drag-over-from-folder');
                }
            });

            shortcutsGrid.addEventListener('drop', (e) => {
                const isFolderItem = e.dataTransfer.getData('application/x-folder-item');
                if (isFolderItem) {
                    e.preventDefault();
                    e.stopPropagation();

                    const itemId = e.dataTransfer.getData('text/plain');
                    console.log('图标放置到主页面网格:', itemId);

                    this.moveItemOutOfFolder(itemId, e.clientX, e.clientY);
                    this.clearDragIndicators();
                    this.isDraggedOutside = false;
                }
            });
        }

        // 监听拖拽离开事件
        this.boundHandlers.dragLeave = (e) => {
            const shortcutsGrid = document.querySelector('.shortcuts-grid');
            if (shortcutsGrid && !shortcutsGrid.contains(e.relatedTarget)) {
                shortcutsGrid.classList.remove('drag-over-from-folder');
            }
        };

        document.addEventListener('dragleave', this.boundHandlers.dragLeave);
    }

    /**
     * 清理拖拽事件监听器
     */
    cleanupDragEvents() {
        if (this.boundHandlers.dragOver) {
            document.removeEventListener('dragover', this.boundHandlers.dragOver);
        }
        if (this.boundHandlers.drop) {
            document.removeEventListener('drop', this.boundHandlers.drop);
        }
        if (this.boundHandlers.dragLeave) {
            document.removeEventListener('dragleave', this.boundHandlers.dragLeave);
        }
    }

    /**
     * 清理拖拽指示器
     */
    clearDragIndicators() {
        const folderModal = document.getElementById('folderModal');
        if (folderModal) {
            folderModal.classList.remove('drag-out-indicator');
        }

        const shortcutsGrid = document.querySelector('.shortcuts-grid');
        if (shortcutsGrid) {
            shortcutsGrid.classList.remove('drag-over-from-folder');
        }
    }

    /**
     * 将图标从文件夹中移出
     * @param {string} itemId 图标ID
     * @param {number} x 鼠标X坐标
     * @param {number} y 鼠标Y坐标
     */
    moveItemOutOfFolder(itemId, x, y) {
        if (!this.currentFolder) {
            console.warn('没有当前文件夹，无法移出图标');
            return;
        }

        // 从文件夹中移除图标
        const itemIndex = this.currentFolder.children.findIndex(child => child.id == itemId);
        if (itemIndex === -1) {
            console.warn('在文件夹中未找到图标:', itemId);
            return;
        }

        const item = this.currentFolder.children[itemIndex];
        this.currentFolder.children.splice(itemIndex, 1);

        console.log('从文件夹中移除图标:', item.name);

        // 清理图标的父级关系
        delete item.parentId;

        // 将图标添加回主界面，设置合适的order
        const maxOrder = Math.max(0, ...this.shortcutManager.shortcuts.map(s => s.order || 0));
        item.order = maxOrder + 1;

        this.shortcutManager.shortcuts.push(item);

        // 更新文件夹的更新时间
        this.currentFolder.updatedAt = Date.now();

        // 保存数据
        this.shortcutManager.saveShortcuts();

        // 如果文件夹只剩一个图标，解散文件夹
        if (this.currentFolder.children.length === 1) {
            console.log('文件夹只剩一个图标，解散文件夹');
            this.dissolveFolder(this.currentFolder.id);

            // 关闭文件夹弹窗
            const folderModal = document.getElementById('folderModal');
            if (folderModal) {
                folderModal.style.display = 'none';
            }
            this.currentFolder = null;
        } else if (this.currentFolder.children.length === 0) {
            // 文件夹为空，删除文件夹
            console.log('文件夹为空，删除文件夹');
            this.shortcutManager.removeShortcut(this.currentFolder.id);

            // 关闭文件夹弹窗
            const folderModal = document.getElementById('folderModal');
            if (folderModal) {
                folderModal.style.display = 'none';
            }
            this.currentFolder = null;
        } else {
            // 重新渲染文件夹内容
            this.renderFolderContent(this.currentFolder);
        }

        // 重新渲染主界面
        this.shortcutManager.render();

        console.log('图标成功移出文件夹并添加到主页面');
    }

    /**
     * 更新文件夹名称
     * @param {string} folderId 文件夹ID
     * @param {string} newName 新名称
     */
    updateFolderName(folderId, newName) {
        const folder = this.shortcutManager.getShortcutById(folderId);
        if (folder && folder.type === 'folder') {
            folder.name = newName.trim() || '未命名文件夹';
            folder.updatedAt = Date.now();

            // 保存并重新渲染主界面
            this.shortcutManager.saveShortcuts();
            this.shortcutManager.render();
        }
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FolderManager;
}
