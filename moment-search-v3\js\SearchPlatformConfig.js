/**
 * 搜索平台配置
 * 定义不同平台的URL模式，支持常规搜索和快捷搜索两种模式
 */

class SearchPlatformConfig {
    constructor() {
        // 搜索平台配置数据
        this.platforms = this.initializePlatforms();
    }

    /**
     * 初始化搜索平台配置
     * @returns {Array} 平台配置数组
     */
    initializePlatforms() {
        return [
            // 核心搜索引擎 - 只保留3个主要平台
            {
                id: 'google',
                name: 'Google',
                icon: '🔍',
                category: 'search',
                urls: {
                    normal: 'https://www.google.com',
                    search: 'https://www.google.com/search?q={keyword}'
                },
                description: '全球搜索引擎'
            },
            {
                id: 'bing',
                name: 'Bing',
                icon: '🌐',
                category: 'search',
                urls: {
                    normal: 'https://www.bing.com',
                    search: 'https://www.bing.com/search?q={keyword}'
                },
                description: '微软搜索引擎'
            },
            {
                id: 'baidu',
                name: '百度',
                icon: '🔎',
                category: 'search',
                urls: {
                    normal: 'https://www.baidu.com',
                    search: 'https://www.baidu.com/s?wd={keyword}'
                },
                description: '中文搜索引擎'
            }
        ];
    }

    /**
     * 根据ID获取平台配置
     * @param {string} platformId 平台ID
     * @returns {Object|null} 平台配置对象
     */
    getPlatform(platformId) {
        return this.platforms.find(platform => platform.id === platformId) || null;
    }

    /**
     * 根据分类获取平台列表
     * @param {string} category 分类名称
     * @returns {Array} 平台配置数组
     */
    getPlatformsByCategory(category) {
        return this.platforms.filter(platform => platform.category === category);
    }

    /**
     * 获取所有分类
     * @returns {Array} 分类数组
     */
    getCategories() {
        const categories = [...new Set(this.platforms.map(p => p.category))];
        return categories.map(category => ({
            id: category,
            name: this.getCategoryDisplayName(category),
            platforms: this.getPlatformsByCategory(category)
        }));
    }

    /**
     * 获取分类显示名称
     * @param {string} category 分类ID
     * @returns {string} 显示名称
     */
    getCategoryDisplayName(category) {
        const categoryNames = {
            'social': '社交媒体',
            'video': '视频平台',
            'education': '学习平台',
            'shopping': '购物平台',
            'development': '开发平台',
            'search': '搜索引擎'
        };
        return categoryNames[category] || category;
    }

    /**
     * 根据URL模式创建快捷方式数据
     * @param {string} platformId 平台ID
     * @param {string} mode 模式：normal(常规) / quick(快捷搜索)
     * @returns {Object|null} 快捷方式数据
     */
    createShortcutData(platformId, mode = 'normal') {
        const platform = this.getPlatform(platformId);
        if (!platform) return null;

        const baseData = {
            id: Date.now() + Math.random(),
            name: platform.name,
            icon: platform.icon,
            category: platform.category,
            order: 0,
            view: 0,
            type: 'icon',
            faviconUrl: '',
            faviconCacheTime: null
        };

        if (mode === 'quick') {
            // 快捷搜索模式
            return {
                ...baseData,
                url: platform.urls.normal, // 用于显示和图标获取
                searchUrl: platform.urls.search, // 用于搜索跳转
                searchMode: 'search',
                pageMode: 'quick'
            };
        } else {
            // 常规模式
            return {
                ...baseData,
                url: platform.urls.normal,
                searchMode: 'direct',
                pageMode: 'normal'
            };
        }
    }

    /**
     * 批量创建快捷方式数据
     * @param {Array} platformIds 平台ID数组
     * @param {string} mode 模式
     * @returns {Array} 快捷方式数据数组
     */
    createBatchShortcutData(platformIds, mode = 'normal') {
        return platformIds.map((platformId, index) => {
            const shortcut = this.createShortcutData(platformId, mode);
            if (shortcut) {
                shortcut.order = index + 1;
            }
            return shortcut;
        }).filter(Boolean);
    }

    /**
     * 验证搜索URL模板
     * @param {string} urlTemplate URL模板
     * @returns {boolean} 是否有效
     */
    validateSearchUrlTemplate(urlTemplate) {
        return urlTemplate && urlTemplate.includes('{keyword}');
    }

    /**
     * 生成搜索URL
     * @param {string} urlTemplate URL模板
     * @param {string} keyword 搜索关键词
     * @returns {string} 生成的搜索URL
     */
    generateSearchUrl(urlTemplate, keyword) {
        if (!this.validateSearchUrlTemplate(urlTemplate)) {
            return urlTemplate;
        }
        
        return urlTemplate.replace('{keyword}', encodeURIComponent(keyword));
    }
}

// 导出类
window.SearchPlatformConfig = SearchPlatformConfig;
